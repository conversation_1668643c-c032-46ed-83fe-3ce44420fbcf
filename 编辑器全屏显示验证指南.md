# 编辑器全屏显示验证指南

## 🚀 快速启动验证

### 1. 重新启动系统

```powershell
# 停止现有服务
.\stop-windows.ps1

# 重新构建并启动所有服务
.\start-windows.ps1 -Build

# 等待所有服务启动完成(约5-10分钟)
```

### 2. 访问系统

```
浏览器打开: http://localhost:80
```

## ✅ 验证清单

### 阶段1：基础功能验证

#### 1.1 登录验证
- [ ] 能够正常访问登录页面
- [ ] 能够成功登录系统
- [ ] 登录后自动跳转到项目管理页面

#### 1.2 项目管理页面验证
- [ ] 项目列表正常显示
- [ ] 项目卡片可以正常点击
- [ ] 项目操作按钮正常工作

### 阶段2：编辑器全屏显示验证 🎯

#### 2.1 编辑器访问验证
- [ ] **关键验证**：点击项目卡片后，编辑器占据整个浏览器窗口
- [ ] **关键验证**：没有左侧导航栏显示
- [ ] **关键验证**：没有顶部应用导航栏显示
- [ ] 编辑器界面完整显示

#### 2.2 编辑器布局验证
- [ ] 顶部工具栏正常显示（包含项目名称和Publish按钮）
- [ ] 左侧面板显示：Hierarchy（上）+ Material Library（下）
- [ ] 中间区域显示：View port（上）+ Assets（下）
- [ ] 右侧面板显示：Models（上）+ Properties（下）
- [ ] 场景视口工具栏完整显示

#### 2.3 编辑器功能验证
- [ ] 3D场景正常渲染
- [ ] 所有面板可以正常操作
- [ ] 工具栏按钮响应正常
- [ ] 面板可以拖拽和调整大小

### 阶段3：导航和返回验证

#### 3.1 URL验证
- [ ] 编辑器URL格式正确：`/editor/{projectId}/{sceneId}`
- [ ] 直接访问编辑器URL能正常工作
- [ ] 刷新页面后编辑器状态保持

#### 3.2 返回功能验证
- [ ] 可以通过浏览器后退按钮返回项目列表
- [ ] 编辑器内的返回按钮正常工作
- [ ] 返回后项目列表状态正常

## 🔍 详细验证步骤

### 步骤1：登录系统
1. 打开浏览器，访问 `http://localhost:80`
2. 输入用户名和密码登录
3. **验证**：成功登录后应该看到项目管理页面

### 步骤2：进入编辑器（关键步骤）
1. 在项目管理页面，点击任意项目卡片
2. **关键验证**：观察页面变化
   - ✅ 应该看到：编辑器占据整个浏览器窗口
   - ❌ 不应该看到：左侧导航栏、顶部应用栏
3. **验证编辑器布局**：
   - 顶部：工具栏（包含项目名称和Publish按钮）
   - 左侧：Hierarchy + Material Library
   - 中间：View port + Assets
   - 右侧：Models + Properties

### 步骤3：功能测试
1. **场景渲染测试**：
   - 3D场景应该正常显示
   - 可以旋转、缩放视图
   
2. **面板操作测试**：
   - 点击不同面板标签
   - 拖拽面板边界调整大小
   - 所有操作应该响应正常

3. **工具栏测试**：
   - 点击顶部工具栏按钮
   - 点击场景视口工具栏按钮
   - 所有按钮应该有响应

### 步骤4：导航测试
1. **URL测试**：
   - 检查地址栏URL格式：`/editor/{projectId}/{sceneId}`
   - 复制URL到新标签页，应该能直接打开编辑器
   
2. **返回测试**：
   - 点击浏览器后退按钮
   - 应该返回到项目管理页面
   - 项目列表应该正常显示

## 🐛 常见问题排查

### 问题1：编辑器仍然显示在子窗口中
**现象**：点击项目卡片后，编辑器左侧仍有导航栏

**排查步骤**：
1. 检查浏览器缓存：按 `Ctrl+F5` 强制刷新
2. 检查URL：应该是 `/editor/{projectId}/{sceneId}`，不是 `/projects`
3. 重新构建：`.\stop-windows.ps1` 然后 `.\start-windows.ps1 -Build`

### 问题2：编辑器加载失败
**现象**：点击项目卡片后显示错误页面

**排查步骤**：
1. 检查浏览器控制台错误信息
2. 检查网络请求是否正常
3. 查看Docker容器日志：`docker-compose -f docker-compose.windows.yml logs editor`

### 问题3：编辑器布局异常
**现象**：编辑器显示但布局不正确

**排查步骤**：
1. 检查是否是之前的布局修复生效
2. 清除浏览器缓存和本地存储
3. 检查控制台是否有JavaScript错误

## 📊 验证报告模板

### 验证结果记录

**验证时间**：_____________

**验证环境**：
- 操作系统：Windows
- 浏览器：_____________
- Docker版本：_____________

**验证结果**：

#### 基础功能 ✅/❌
- [ ] 登录功能正常
- [ ] 项目列表显示正常
- [ ] 项目卡片点击正常

#### 编辑器全屏显示 ✅/❌
- [ ] **核心验证**：编辑器占据整个浏览器窗口
- [ ] **核心验证**：没有左侧导航栏
- [ ] **核心验证**：没有顶部应用栏
- [ ] 编辑器布局正确

#### 编辑器功能 ✅/❌
- [ ] 3D场景正常渲染
- [ ] 面板操作正常
- [ ] 工具栏功能正常
- [ ] 导航返回正常

**问题记录**：
_________________________________
_________________________________
_________________________________

**总体评价**：
- [ ] ✅ 修复成功，编辑器全屏显示正常
- [ ] ⚠️ 部分功能正常，有小问题
- [ ] ❌ 修复失败，需要进一步调试

## 🎯 成功标志

当以下所有条件都满足时，说明修复成功：

1. ✅ **编辑器全屏显示**：点击项目卡片后，编辑器占据整个浏览器窗口
2. ✅ **布局正确**：四区域布局完整显示，所有面板标题为英文
3. ✅ **功能正常**：3D场景渲染、面板操作、工具栏功能都正常
4. ✅ **导航正常**：可以正常进入和退出编辑器
5. ✅ **无错误**：浏览器控制台没有关键错误信息

## 📞 获取帮助

如果验证过程中遇到问题：

1. **查看详细报告**：[编辑器全屏显示修复报告.md](./编辑器全屏显示修复报告.md)
2. **检查配置文件**：确保 `.env`、`docker-compose.windows.yml` 等配置正确
3. **查看日志**：`docker-compose -f docker-compose.windows.yml logs`
4. **重新构建**：`.\start-windows.ps1 -Build -Clean`

---

**验证指南版本**: 1.0  
**最后更新**: 2025-10-01
