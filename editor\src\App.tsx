/**
 * 应用程序主组件
 */
import React, { useEffect } from 'react';
import { Routes, Route, Navigate, useNavigate } from 'react-router-dom';
import { ConfigProvider, theme, Spin } from 'antd';
import { useTranslation } from 'react-i18next';

import { useAppDispatch, useAppSelector } from './store';
import { checkAuth, logout } from './store/auth/authSlice';
import { ThemeType } from './store/ui/uiSlice';
import { selectShowConflictPanel, clearConflicts, setShowConflictPanel } from './store/collaboration/conflictSlice';
import { setShowConflictPanel as setGitShowConflictPanel, setMergeConflicts } from './store/git/gitSlice';
import { AppLayout } from './components/layout/AppLayout';
import { LoginPage } from './pages/LoginPage';
import { RegisterPage } from './pages/RegisterPage';
import { ProjectsPage } from './pages/ProjectsPage';
import { EditorPage } from './pages/EditorPage';
import { NotFoundPage } from './pages/NotFoundPage';
import FeedbackSystemDemo from './pages/FeedbackSystemDemo';
import SettingsPage from './pages/SettingsPage';
import HelpPage from './pages/HelpPage';
import AchievementNotification from './components/achievements/AchievementNotification';
import TutorialHighlightContainer from './components/tutorials/TutorialHighlight';
import GitPanel from './components/git/GitPanel';
import GitConflictResolver from './components/git/GitConflictResolver';
import ConflictPanel from './components/collaboration/ConflictPanel';
import { microserviceIntegration } from './services/MicroserviceIntegration';
import { apiClient } from './services/ApiClient';
import { config } from './config/environment';
import './utils/testFixes';
import { DevToolsFloatButton } from './components/debug/DevToolsPanel';
import { ErrorBoundary } from './components/common/ErrorBoundary';

const App: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { i18n } = useTranslation();

  // 使用try-catch包装useSelector调用，防止Context错误
  const authState = useAppSelector((state) => {
    try {
      return state?.auth || { isAuthenticated: false, isLoading: true, user: null, token: null, error: null };
    } catch (error) {
      console.warn('Auth state access error:', error);
      return { isAuthenticated: false, isLoading: true, user: null, token: null, error: null };
    }
  });

  const uiState = useAppSelector((state) => {
    try {
      return state?.ui || { theme: 'light', language: 'zh-CN' };
    } catch (error) {
      console.warn('UI state access error:', error);
      return { theme: 'light', language: 'zh-CN' };
    }
  });

  const { isAuthenticated, isLoading } = authState;
  const { theme: themeType, language } = uiState;

  // 获取冲突面板显示状态
  const showConflictPanel = useAppSelector(selectShowConflictPanel);

  // 检查当前路径，只在编辑器页面显示冲突面板
  const currentPath = window.location.pathname;
  const isEditorPage = currentPath.includes('/editor/') ||
                      currentPath.includes('/terrain-editor') ||
                      currentPath.includes('/feedback-demo');

  // 强制在项目管理页面隐藏冲突面板
  const isProjectsPage = currentPath.includes('/projects') || currentPath === '/';
  const shouldShowConflictPanel = showConflictPanel && isEditorPage && !isProjectsPage;

  // 检查认证状态
  useEffect(() => {
    try {
      // 清除可能残留的冲突数据，确保登录后不会显示旧的冲突窗口
      if (config.enableDebug) {
        console.log('🧹 清理应用状态...');
      }

      // 清除所有冲突数据和冲突面板状态
      dispatch(clearConflicts());
      dispatch(setShowConflictPanel(false));

      // 清除Git冲突状态
      dispatch(setGitShowConflictPanel(false));
      dispatch(setMergeConflicts([]));

      // 检查localStorage和sessionStorage中是否有token
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      if (token) {
        dispatch(checkAuth());
      }
    } catch (error) {
      console.error('Auth check error:', error);
    }
  }, [dispatch]);

  // 监听路径变化，确保在项目管理页面隐藏冲突面板
  useEffect(() => {
    if (isProjectsPage && showConflictPanel) {
      console.log('🔒 在项目管理页面强制关闭冲突面板');
      dispatch(setShowConflictPanel(false));
      dispatch(clearConflicts());

      // 同时清除Git冲突状态
      dispatch(setGitShowConflictPanel(false));
      dispatch(setMergeConflicts([]));
    }
  }, [isProjectsPage, showConflictPanel, dispatch]);

  // 设置语言 - 强制使用中文
  useEffect(() => {
    // 强制设置为中文，忽略任何其他语言设置
    if (i18n.language !== 'zh-CN') {
      console.log('🔧 App.tsx: 强制设置语言为中文，当前语言:', i18n.language);
      i18n.changeLanguage('zh-CN').then(() => {
        console.log('✅ App.tsx: 语言已设置为中文');
      }).catch((error) => {
        console.error('❌ App.tsx: 设置语言失败:', error);
      });
    }
  }, [i18n]);

  // 监听语言变化，确保始终为中文
  useEffect(() => {
    const handleLanguageChange = (lng) => {
      if (lng !== 'zh-CN') {
        console.log('⚠️ App.tsx: 检测到语言变化为', lng, '，强制切换回中文');
        i18n.changeLanguage('zh-CN');
      }
    };

    i18n.on('languageChanged', handleLanguageChange);

    return () => {
      i18n.off('languageChanged', handleLanguageChange);
    };
  }, [i18n]);

  // 监听认证事件
  useEffect(() => {
    const handleUnauthorized = () => {
      // 清除认证状态并跳转到登录页
      dispatch(logout());
      navigate('/login', { replace: true });
    };

    // 监听API客户端的未授权事件
    apiClient.on('auth:unauthorized', handleUnauthorized);

    return () => {
      apiClient.off('auth:unauthorized', handleUnauthorized);
    };
  }, [dispatch, navigate]);

  // 初始化微服务集成 - 只在用户已认证且不在加载状态时初始化
  // 延迟初始化，避免在项目管理页面触发不必要的服务
  useEffect(() => {
    if (!isAuthenticated || isLoading) {
      return;
    }

    // 只在编辑器页面或特定需要的页面初始化微服务集成
    const currentPath = window.location.pathname;
    const needsMicroservices = currentPath.includes('/editor/') ||
                              currentPath.includes('/terrain-editor') ||
                              currentPath.includes('/feedback-demo');

    if (!needsMicroservices) {
      if (config.enableDebug) {
        console.log('🔄 跳过微服务集成初始化 - 当前页面不需要:', currentPath);
      }
      return;
    }

    const initMicroservices = async () => {
      try {
        if (config.enableDebug) {
          console.log('🚀 初始化微服务集成...');
        }

        // 添加延迟，确保认证状态稳定
        await new Promise(resolve => setTimeout(resolve, 2000));

        await microserviceIntegration.initialize();
        if (config.enableDebug) {
          console.log('✅ 微服务集成初始化完成');
        }
      } catch (error) {
        console.warn('⚠️ 微服务集成初始化遇到问题，但应用将继续运行:', error);
      }
    };

    // 延迟初始化，避免在登录后立即触发
    const timer = setTimeout(initMicroservices, 3000);

    // 清理函数
    return () => {
      clearTimeout(timer);
      microserviceIntegration.destroy();
    };
  }, [isAuthenticated, isLoading]);

  // 如果正在加载认证状态，显示加载中
  if (isLoading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }

  return (
    <ConfigProvider
      theme={{
        algorithm: themeType === ThemeType.DARK ? theme.darkAlgorithm : theme.defaultAlgorithm}}
    >
      <ErrorBoundary fallback={
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          flexDirection: 'column',
          padding: '20px'
        }}>
          <h2>应用出现错误</h2>
          <p>很抱歉，应用遇到了一个意外错误。请刷新页面重试。</p>
          <button onClick={() => window.location.reload()}>刷新页面</button>
        </div>
      }>
        <Routes>
          {/* 公共路由 */}
          <Route path="/login" element={isAuthenticated ? <Navigate to="/projects" /> : <LoginPage />} />
          <Route path="/register" element={isAuthenticated ? <Navigate to="/projects" /> : <RegisterPage />} />

          {/* 需要认证的路由 */}
          <Route path="/" element={isAuthenticated ? <AppLayout /> : <Navigate to="/login" />}>
            <Route index element={<Navigate to="/projects" />} />
            <Route path="projects" element={<ProjectsPage />} />
            <Route path="feedback-demo" element={
              <ErrorBoundary fallback={
                <div style={{ padding: '20px', textAlign: 'center' }}>
                  <h3>反馈系统演示加载失败</h3>
                  <p>反馈系统演示遇到了问题，请尝试刷新页面。</p>
                  <button onClick={() => window.location.reload()}>刷新页面</button>
                </div>
              }>
                <FeedbackSystemDemo />
              </ErrorBoundary>
            } />
            <Route path="settings" element={<SettingsPage />} />
            <Route path="help" element={<HelpPage />} />
            <Route path="profile" element={<Navigate to="/settings" replace />} />
          </Route>

          {/* 编辑器全屏模式 - 独立路由，不在AppLayout内部 */}
          <Route path="/editor/:projectId/:sceneId" element={
            isAuthenticated ? (
              <ErrorBoundary fallback={
                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '100vh',
                  background: '#f5f5f5'
                }}>
                  <h3>编辑器加载失败</h3>
                  <p>编辑器遇到了问题，请尝试刷新页面或返回项目列表。</p>
                  <div style={{ marginTop: '20px' }}>
                    <button
                      onClick={() => window.location.reload()}
                      style={{ marginRight: '10px' }}
                    >
                      刷新页面
                    </button>
                    <button onClick={() => window.location.href = '/projects'}>
                      返回项目列表
                    </button>
                  </div>
                </div>
              }>
                <EditorPage />
              </ErrorBoundary>
            ) : (
              <Navigate to="/login" />
            )
          } />

          {/* 编辑器全屏模式的别名路由 */}
          <Route path="/editor/:projectId/:sceneId/fullscreen" element={
            isAuthenticated ? (
              <ErrorBoundary fallback={
                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '100vh',
                  background: '#f5f5f5'
                }}>
                  <h3>全屏编辑器加载失败</h3>
                  <p>全屏编辑器遇到了问题，请尝试刷新页面。</p>
                  <div style={{ marginTop: '20px' }}>
                    <button
                      onClick={() => window.location.reload()}
                      style={{ marginRight: '10px' }}
                    >
                      刷新页面
                    </button>
                    <button onClick={() => window.location.href = '/projects'}>
                      返回项目列表
                    </button>
                  </div>
                </div>
              }>
                <EditorPage />
              </ErrorBoundary>
            ) : (
              <Navigate to="/login" />
            )
          } />

          {/* 404页面 */}
          <Route path="*" element={<NotFoundPage />} />
        </Routes>

        {/* 全局组件 */}
        {isAuthenticated && (
          <>
            <AchievementNotification />
            <TutorialHighlightContainer />
            <GitPanel />
            <GitConflictResolver />
            {/* 全局冲突面板 - 只在编辑器页面显示 */}
            {shouldShowConflictPanel && (
              <div style={{
                position: 'fixed',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                zIndex: 1000,
                maxWidth: '90vw',
                maxHeight: '90vh',
                overflow: 'auto'
              }}>
                <ConflictPanel />
              </div>
            )}
          </>
        )}

        {/* 开发工具 */}
        <DevToolsFloatButton />
      </ErrorBoundary>
    </ConfigProvider>
  );
};

export default App;
